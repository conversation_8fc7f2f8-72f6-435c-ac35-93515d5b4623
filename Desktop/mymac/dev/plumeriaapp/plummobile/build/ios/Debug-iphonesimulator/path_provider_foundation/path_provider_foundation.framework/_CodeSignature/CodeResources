<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/path_provider_foundation-Swift.h</key>
		<data>
		UElvDhE9kGoNu6wT6qGh/l2wBtg=
		</data>
		<key>Headers/path_provider_foundation-umbrella.h</key>
		<data>
		HLPUH7a1+Uud1CEb8ZVbUJlVow0=
		</data>
		<key>Info.plist</key>
		<data>
		l9QHpU6PBNycinoq3098cr5oBoU=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		4ou7sfZJMUritXkWYrNLe8ROvJ8=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		mCYwn+/L8qDHPGRIf6Huhz9/ewY=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		RHMr6oxuGoj8uGVn3a/z89sW5ec=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		zIZvxSuWKhCGWMdRizcVje8GrXs=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		KGkCjQP/TB8w+Qc4taKUNV6ow0s=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		fWhXyMPUWL4hlS5jP3q/LZEJ6t8=
		</data>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		L5dy61dyp6EjpPgMbxwMFjYM/wE=
		</data>
		<key>path_provider_foundation_privacy.bundle/Info.plist</key>
		<data>
		29+tf9UNrLYHgUAQVXT4KwVWja4=
		</data>
		<key>path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/path_provider_foundation-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			vQvrWyoMJPDxhLvUTxjEwQyx01CEXuzs9y0g2d76Aj0=
			</data>
		</dict>
		<key>Headers/path_provider_foundation-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zQc9a/y7t7MqxTgSSmpfImDb6DSiKF26gJFqk0CuTBE=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			GljPXu0mFl3W6UpwpsufQgltdn4I8vCubbMMs3ACbiw=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			IETtHoh73tRSgTRtqLBRE7DzyXIGSEEDxnqgJ+09AW0=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			K3xonyPv3bsrWrWhPDpQxOfPHSiBKbtBAjksk1epp/I=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			BYdW9dzcxixqWPFXMrFHxrjJHO+o7J5ogLxmWZqE/lo=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			Bt9a2wUAPhuiTHcfTKU8WzLtx9rDMS1moyWOTjHBSMo=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			1/MqF30VZ3rvJ/+G7kcAyIuk011zr+stwgEbwvu+BAw=
			</data>
		</dict>
		<key>Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			uM3TULKETeGuqbl+ZtlTgGqEu9KtHcL2qOqqCXHdEdg=
			</data>
		</dict>
		<key>path_provider_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			EMJz/f3MESdxzP7P1uxeJObZoII/kxCmUIJDXXpNnf4=
			</data>
		</dict>
		<key>path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
