{"": {"diagnostics": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/flutter_secure_storage_macos-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/flutter_secure_storage_macos-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/flutter_secure_storage_macos-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/flutter_secure_storage_macos-master.swiftdeps"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/macos/Classes/FlutterSecureStorage.swift": {"const-values": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStorage.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStorage.d", "diagnostics": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStorage.dia", "index-unit-output-path": "/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStorage.o", "llvm-bc": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStorage.bc", "object": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStorage.o", "swift-dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStorage.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStorage~partial.swiftmodule"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/macos/Classes/FlutterSecureStoragePlugin.swift": {"const-values": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStoragePlugin.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStoragePlugin.d", "diagnostics": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStoragePlugin.dia", "index-unit-output-path": "/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStoragePlugin.o", "llvm-bc": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStoragePlugin.bc", "object": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStoragePlugin.o", "swift-dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStoragePlugin.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/Objects-normal/arm64/FlutterSecureStoragePlugin~partial.swiftmodule"}}