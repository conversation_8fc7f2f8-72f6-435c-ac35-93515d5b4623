-target arm64-apple-macos10.14 '-std=gnu11' -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/ModuleCache.noindex' '-fmodule-name=flutter_secure_storage_macos' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -g -iquote /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/flutter_secure_storage_macos-generated-files.hmap -I/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/flutter_secure_storage_macos-own-target-headers.hmap -I/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/flutter_secure_storage_macos-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-8699adb1dd336b26511df848a716bd42-VFS/all-product-headers.yaml -iquote /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/flutter_secure_storage_macos-project-headers.hmap -I/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/flutter_secure_storage_macos/include -I/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/DerivedSources-normal/arm64 -I/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/DerivedSources/arm64 -I/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/DerivedSources -F/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/flutter_secure_storage_macos -F/Users/<USER>/Desktop/mymac/development/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64