{"name": "plumeria-branding-api", "version": "1.0.0", "description": "API for managing app branding configuration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["branding", "api", "construction", "management"], "author": "Plumeria Team", "license": "ISC", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}}