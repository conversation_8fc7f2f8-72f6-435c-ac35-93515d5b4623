<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/shared_preferences_foundation-Swift.h</key>
		<data>
		4IenC7NNLCaH6Ab8TX0LzsUBU1M=
		</data>
		<key>Headers/shared_preferences_foundation-umbrella.h</key>
		<data>
		3HwT7vyU2akEuk9WxLekNTic7eA=
		</data>
		<key>Info.plist</key>
		<data>
		h2w4OGyw/q5ShdIEyPYBXRm4CD4=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		2JlPqRKaQWgT2bb1vkexg1Nq/sA=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		YLC8SsG9zg7zz+k9MJhKW69jK1Y=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		lMhTLiQGtB2X0Ed0QikaSt+Dy78=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		zkVzg+qugbv8RPMatXJcZIHmiPU=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		eR25a7lLQS249rzX9FjJMWFgk3g=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		WgNhR1mpItAKV/KHiRR1/ewKXqM=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		m6AJNmnAesEqqYxltXC4rVdwTRc=
		</data>
		<key>shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<data>
		RiENOI6JPJ/VvYMJpafFUMZFQ4U=
		</data>
		<key>shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6uLTlq7fgHHWA8emYDf4ImHC+AY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/shared_preferences_foundation-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jiefGRAqEo/RbJIrEFdgk09hs54NUyp+QQMLf8loV3M=
			</data>
		</dict>
		<key>Headers/shared_preferences_foundation-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			su5Uv36/prKNtVojyB1A6T1k258lLooi1Hi7YUVwbbA=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			Q0UmBz/CGB1z75ZFA4+mlzbd0g+wymylVwJDqUxGkZM=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			NDtYVsaB1YIljy0DAQgxUbkIN9Bj/i1lx4IgSmVtPZA=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			CLL1li/i0yRYOTpuwuLnZ5JE0uasf8vEiDcK7NHGBSs=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			h0yK/drqq0HFyVdBgDtkbtzEZQ2UQiHCMMy4LryCMx8=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			9GOpk+MJE9beThgjSwPt+t2V5i/V4F3qxZjS0IsYo0Y=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			+02zo1NV0HkoJ/95Rq3yL9ylJksKQvHCqb0gGQWKico=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			lxm3sRAp3fU3Cf1HUWk2JFXAmtAQBNfMiBH5W4hw3Rk=
			</data>
		</dict>
		<key>shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			wu0I2ITA9q+z8FaIcgDog3mRcClfiZZMRzktME1Hcwo=
			</data>
		</dict>
		<key>shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BWQouTi9VwGKYAGdFcPB7i+nJ/I2h1mLu9k0hIsYCxo=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
