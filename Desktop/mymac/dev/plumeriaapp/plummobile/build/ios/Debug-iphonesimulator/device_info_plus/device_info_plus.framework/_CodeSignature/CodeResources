<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/DeviceIdentifiers.h</key>
		<data>
		ArzKyFeM4EJkCwi6Bby3B3jXcRU=
		</data>
		<key>Headers/FPPDeviceInfoPlusPlugin.h</key>
		<data>
		jK2eBNr3bLIjDaI4U4nnxjZLcsY=
		</data>
		<key>Headers/device_info_plus-umbrella.h</key>
		<data>
		vPxjbVlNEkVRCUsUyFg15NIeapM=
		</data>
		<key>Info.plist</key>
		<data>
		WgEWdiL4/K9r4Wm5BoVGR+4EdGA=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		FgvfDWSicujjjNMktmC7NHiARtQ=
		</data>
		<key>device_info_plus_privacy.bundle/Info.plist</key>
		<data>
		fManEFFIh4VtcCT/c3VHqXF9KC4=
		</data>
		<key>device_info_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/DeviceIdentifiers.h</key>
		<dict>
			<key>hash2</key>
			<data>
			qSGylfRDt9cwMiAVvy4Tl/bX2w3stxsa4BGStlMYDjo=
			</data>
		</dict>
		<key>Headers/FPPDeviceInfoPlusPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4Pcm+PvvGkUNIC+muxNwlwDn24fmlZvG7kR+9sMdHhs=
			</data>
		</dict>
		<key>Headers/device_info_plus-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SpnxtUlg/k85XieTPw1L6DB5zNIwxoX0okXZK0XDSVE=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			nT9sjRCS7UxfpwNi9AnFhCxfwvEP2AsKDJH3D8Rmc5E=
			</data>
		</dict>
		<key>device_info_plus_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			d1epaPjEhIcMDmq2tPddDuWW629sfeMesZQfGH0rN1I=
			</data>
		</dict>
		<key>device_info_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
