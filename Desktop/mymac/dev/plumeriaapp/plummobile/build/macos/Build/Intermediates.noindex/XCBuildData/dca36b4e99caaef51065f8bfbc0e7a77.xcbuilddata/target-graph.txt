Target dependency graph (12 targets)
Target 'Runner' in project 'Runner'
➜ Explicit dependency on target 'Flutter Assemble' in project 'Runner'
➜ Implicit dependency on target 'Pods-Runner' in project 'Pods' via file 'Pods_Runner.framework' in build phase 'Link Binary'
➜ Implicit dependency on target 'device_info_plus' in project 'Pods' via options '-framework device_info_plus' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'flutter_secure_storage_macos' in project 'Pods' via options '-framework flutter_secure_storage_macos' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'path_provider_foundation' in project 'Pods' via options '-framework path_provider_foundation' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'shared_preferences_foundation' in project 'Pods' via options '-framework shared_preferences_foundation' in build setting 'OTHER_LDFLAGS'
Target 'Pods-Runner' in project 'Pods'
➜ Explicit dependency on target 'FlutterMacOS' in project 'Pods'
➜ Explicit dependency on target 'device_info_plus' in project 'Pods'
➜ Explicit dependency on target 'flutter_secure_storage_macos' in project 'Pods'
➜ Explicit dependency on target 'path_provider_foundation' in project 'Pods'
➜ Explicit dependency on target 'shared_preferences_foundation' in project 'Pods'
Target 'Flutter Assemble' in project 'Runner'
➜ Implicit dependency on target 'device_info_plus' in project 'Pods' via options '-framework device_info_plus' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'flutter_secure_storage_macos' in project 'Pods' via options '-framework flutter_secure_storage_macos' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'path_provider_foundation' in project 'Pods' via options '-framework path_provider_foundation' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'shared_preferences_foundation' in project 'Pods' via options '-framework shared_preferences_foundation' in build setting 'OTHER_LDFLAGS'
Target 'shared_preferences_foundation' in project 'Pods'
➜ Explicit dependency on target 'FlutterMacOS' in project 'Pods'
➜ Explicit dependency on target 'shared_preferences_foundation-shared_preferences_foundation_privacy' in project 'Pods'
Target 'shared_preferences_foundation-shared_preferences_foundation_privacy' in project 'Pods' (no dependencies)
Target 'path_provider_foundation' in project 'Pods'
➜ Explicit dependency on target 'FlutterMacOS' in project 'Pods'
➜ Explicit dependency on target 'path_provider_foundation-path_provider_foundation_privacy' in project 'Pods'
Target 'path_provider_foundation-path_provider_foundation_privacy' in project 'Pods' (no dependencies)
Target 'flutter_secure_storage_macos' in project 'Pods'
➜ Explicit dependency on target 'FlutterMacOS' in project 'Pods'
➜ Explicit dependency on target 'flutter_secure_storage_macos-flutter_secure_storage_macos' in project 'Pods'
Target 'flutter_secure_storage_macos-flutter_secure_storage_macos' in project 'Pods' (no dependencies)
Target 'device_info_plus' in project 'Pods'
➜ Explicit dependency on target 'FlutterMacOS' in project 'Pods'
➜ Explicit dependency on target 'device_info_plus-device_info_plus_privacy' in project 'Pods'
Target 'device_info_plus-device_info_plus_privacy' in project 'Pods' (no dependencies)
Target 'FlutterMacOS' in project 'Pods' (no dependencies)