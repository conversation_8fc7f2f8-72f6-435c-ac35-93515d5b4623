{"case-sensitive": "false", "roots": [{"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/macos/Pods/Target Support Files/Pods-Runner/Pods-Runner-umbrella.h", "name": "Pods-Runner-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/Pods_Runner.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-Runner.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/Pods_Runner.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/macos/Pods/Target Support Files/device_info_plus/device_info_plus-umbrella.h", "name": "device_info_plus-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/device_info_plus/device_info_plus.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/device_info_plus/device_info_plus.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/device_info_plus/device_info_plus.framework/Headers/device_info_plus-Swift.h", "name": "device_info_plus-Swift.h", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/device_info_plus/device_info_plus.framework/Versions/A/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/macos/Pods/Target Support Files/flutter_secure_storage_macos/flutter_secure_storage_macos-umbrella.h", "name": "flutter_secure_storage_macos-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/flutter_secure_storage_macos/flutter_secure_storage_macos.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/flutter_secure_storage_macos.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/flutter_secure_storage_macos/flutter_secure_storage_macos.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/flutter_secure_storage_macos/flutter_secure_storage_macos.framework/Headers/flutter_secure_storage_macos-Swift.h", "name": "flutter_secure_storage_macos-Swift.h", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/flutter_secure_storage_macos/flutter_secure_storage_macos.framework/Versions/A/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/macos/Pods/Target Support Files/path_provider_foundation/path_provider_foundation-umbrella.h", "name": "path_provider_foundation-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/path_provider_foundation/path_provider_foundation.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/path_provider_foundation.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/path_provider_foundation/path_provider_foundation.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/path_provider_foundation/path_provider_foundation.framework/Headers/path_provider_foundation-Swift.h", "name": "path_provider_foundation-Swift.h", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/path_provider_foundation/path_provider_foundation.framework/Versions/A/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/macos/Pods/Target Support Files/shared_preferences_foundation/shared_preferences_foundation-umbrella.h", "name": "shared_preferences_foundation-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Headers/shared_preferences_foundation-Swift.h", "name": "shared_preferences_foundation-Swift.h", "type": "file"}], "name": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Versions/A/Headers", "type": "directory"}], "version": 0}