{"": {"diagnostics": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/device_info_plus-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/device_info_plus-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/device_info_plus-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/device_info_plus-master.swiftdeps"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/macos/device_info_plus/Sources/device_info_plus/CwlSysctl.swift": {"const-values": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/CwlSysctl.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/CwlSysctl.d", "diagnostics": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/CwlSysctl.dia", "index-unit-output-path": "/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/CwlSysctl.o", "llvm-bc": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/CwlSysctl.bc", "object": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/CwlSysctl.o", "swift-dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/CwlSysctl.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/CwlSysctl~partial.swiftmodule"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/macos/device_info_plus/Sources/device_info_plus/DeviceIdentifiers.swift": {"const-values": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceIdentifiers.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceIdentifiers.d", "diagnostics": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceIdentifiers.dia", "index-unit-output-path": "/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceIdentifiers.o", "llvm-bc": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceIdentifiers.bc", "object": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceIdentifiers.o", "swift-dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceIdentifiers.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceIdentifiers~partial.swiftmodule"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/macos/device_info_plus/Sources/device_info_plus/DeviceInfoPlusMacosPlugin.swift": {"const-values": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceInfoPlusMacosPlugin.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceInfoPlusMacosPlugin.d", "diagnostics": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceInfoPlusMacosPlugin.dia", "index-unit-output-path": "/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceInfoPlusMacosPlugin.o", "llvm-bc": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceInfoPlusMacosPlugin.bc", "object": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceInfoPlusMacosPlugin.o", "swift-dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceInfoPlusMacosPlugin.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/DeviceInfoPlusMacosPlugin~partial.swiftmodule"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/macos/device_info_plus/Sources/device_info_plus/SystemUUID.swift": {"const-values": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/SystemUUID.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/SystemUUID.d", "diagnostics": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/SystemUUID.dia", "index-unit-output-path": "/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/SystemUUID.o", "llvm-bc": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/SystemUUID.bc", "object": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/SystemUUID.o", "swift-dependencies": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/SystemUUID.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Intermediates.noindex/Pods.build/Debug/device_info_plus.build/Objects-normal/arm64/SystemUUID~partial.swiftmodule"}}