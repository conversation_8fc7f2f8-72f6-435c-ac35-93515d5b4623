import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { 
  BrandingConfig, 
  BrandingResponse, 
  BrandingListResponse, 
  BrandingUpdateRequest,
  BrandingVersion,
  DEFAULT_BRANDING 
} from '../models/branding';

@Injectable({
  providedIn: 'root'
})
export class BrandingService {
  private readonly API_URL = `${environment.apiUrl}/api/mobile/v1/branding`;
  private currentBrandingSubject = new BehaviorSubject<BrandingConfig | null>(null);
  public currentBranding$ = this.currentBrandingSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadCurrentBranding();
  }

  /**
   * Get current branding configuration
   */
  getCurrentBranding(): Observable<BrandingConfig> {
    return this.http.get<BrandingResponse>(this.API_URL).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to fetch branding');
      }),
      tap(branding => {
        this.currentBrandingSubject.next(branding);
      })
    );
  }

  /**
   * Update branding configuration
   */
  updateBranding(branding: BrandingUpdateRequest, adminKey: string): Observable<BrandingConfig> {
    const headers = new HttpHeaders({
      'x-admin-key': adminKey
    });

    return this.http.put<BrandingResponse>(this.API_URL, branding, { headers }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to update branding');
      }),
      tap(updatedBranding => {
        this.currentBrandingSubject.next(updatedBranding);
      })
    );
  }

  /**
   * Check branding version
   */
  checkVersion(): Observable<BrandingVersion> {
    return this.http.get<{ success: boolean; data: BrandingVersion; message: string }>(`${this.API_URL}/version`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to check version');
      })
    );
  }

  /**
   * Get branding history (if implemented in backend)
   */
  getBrandingHistory(): Observable<BrandingConfig[]> {
    return this.http.get<BrandingListResponse>(`${this.API_URL}/history`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      })
    );
  }

  /**
   * Upload branding asset
   */
  uploadAsset(file: File, assetType: 'logo' | 'appIcon' | 'splashImage'): Observable<{ url: string }> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('assetType', assetType);

    return this.http.post<{ success: boolean; data: { url: string }; message: string }>(
      `${this.API_URL}/upload-asset`, 
      formData
    ).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to upload asset');
      })
    );
  }

  /**
   * Preview branding changes (without saving)
   */
  previewBranding(branding: BrandingUpdateRequest): Observable<BrandingConfig> {
    return this.http.post<BrandingResponse>(`${this.API_URL}/preview`, branding).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to preview branding');
      })
    );
  }

  /**
   * Reset branding to default
   */
  resetToDefault(adminKey: string): Observable<BrandingConfig> {
    const headers = new HttpHeaders({
      'x-admin-key': adminKey
    });

    return this.http.post<BrandingResponse>(`${this.API_URL}/reset`, {}, { headers }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to reset branding');
      }),
      tap(branding => {
        this.currentBrandingSubject.next(branding);
      })
    );
  }

  /**
   * Validate color format
   */
  isValidColor(color: string): boolean {
    return /^#[0-9A-Fa-f]{6}$/.test(color);
  }

  /**
   * Convert color to RGB
   */
  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  /**
   * Get contrast color (black or white) for given background
   */
  getContrastColor(backgroundColor: string): string {
    const rgb = this.hexToRgb(backgroundColor);
    if (!rgb) return '#000000';
    
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }

  /**
   * Generate CSS variables for theme
   */
  generateCssVariables(branding: BrandingConfig): string {
    return `
      :root {
        --primary-color: ${branding.primaryColor};
        --secondary-color: ${branding.secondaryColor};
        --accent-color: ${branding.accentColor};
        --error-color: ${branding.errorColor};
        --background-color: ${branding.backgroundColor};
        --primary-contrast: ${this.getContrastColor(branding.primaryColor)};
        --secondary-contrast: ${this.getContrastColor(branding.secondaryColor)};
      }
    `;
  }

  /**
   * Apply branding to current page
   */
  applyBrandingToPage(branding: BrandingConfig): void {
    // Update document title
    document.title = `${branding.appName} - Admin Panel`;
    
    // Update CSS variables
    const styleElement = document.getElementById('dynamic-branding-styles') || document.createElement('style');
    styleElement.id = 'dynamic-branding-styles';
    styleElement.innerHTML = this.generateCssVariables(branding);
    
    if (!document.getElementById('dynamic-branding-styles')) {
      document.head.appendChild(styleElement);
    }
  }

  /**
   * Get current branding synchronously
   */
  getCurrentBrandingValue(): BrandingConfig | null {
    return this.currentBrandingSubject.value;
  }

  /**
   * Load current branding on service initialization
   */
  private loadCurrentBranding(): void {
    this.getCurrentBranding().subscribe({
      next: (branding) => {
        this.applyBrandingToPage(branding);
      },
      error: (error) => {
        console.warn('Failed to load branding, using default:', error);
        const defaultBranding = DEFAULT_BRANDING as BrandingConfig;
        this.currentBrandingSubject.next(defaultBranding);
        this.applyBrandingToPage(defaultBranding);
      }
    });
  }
}
