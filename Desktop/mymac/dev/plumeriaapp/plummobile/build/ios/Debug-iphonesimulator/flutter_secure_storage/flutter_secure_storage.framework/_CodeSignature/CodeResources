<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FlutterSecureStoragePlugin.h</key>
		<data>
		E0C2Kdx7GWrmdZjcFStaXab/3mw=
		</data>
		<key>Headers/flutter_secure_storage-Swift.h</key>
		<data>
		9WVu+A7d3otUxXbUvZ92V8H5uCI=
		</data>
		<key>Headers/flutter_secure_storage-umbrella.h</key>
		<data>
		/1OuB+Jfg9W4BgyVCopi7nCgBC0=
		</data>
		<key>Info.plist</key>
		<data>
		YiLVcGWigl7oMaS84jTTZ5VzQX0=
		</data>
		<key>Modules/flutter_secure_storage.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		fK2666KNBbWSpCvClKmH7IwpnWE=
		</data>
		<key>Modules/flutter_secure_storage.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		Map5RbdSCvKAFuzf0fil7ISEkxk=
		</data>
		<key>Modules/flutter_secure_storage.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/flutter_secure_storage.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		OQB7xempX1hs5UvtEC+kc7+X/wE=
		</data>
		<key>Modules/flutter_secure_storage.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		FRj8XX13pbhuhXLIYh37RTGjVGo=
		</data>
		<key>Modules/flutter_secure_storage.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/flutter_secure_storage.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		e74Gx+eV0fbk/CyGUpegZ6g5mtc=
		</data>
		<key>Modules/flutter_secure_storage.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		RpfLcoOKklxkAH5Q2BycXGjdx8U=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		Tjcr9f0mlENastA5iOwyNU2WHqM=
		</data>
		<key>flutter_secure_storage.bundle/Info.plist</key>
		<data>
		EaZKjra/vK6uN7R+m8LwDK4Ra5A=
		</data>
		<key>flutter_secure_storage.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FlutterSecureStoragePlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			O2rk1ffsGJKf5Ee6st3NA4PWvZOCYLFgjQpUJTNsgPw=
			</data>
		</dict>
		<key>Headers/flutter_secure_storage-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			TIIRaB2/6Yzd7b8rbwUP36mm+NBxNOMAQfiolunauy4=
			</data>
		</dict>
		<key>Headers/flutter_secure_storage-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			TjSESZA56FWXyFKUTo4uCUUeWaPHvgOwS9dC2F/MwWI=
			</data>
		</dict>
		<key>Modules/flutter_secure_storage.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			UFDApWC2ARNOFmWOFQnb5CjKisWBKHiwa1M47l++HvI=
			</data>
		</dict>
		<key>Modules/flutter_secure_storage.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			WJov+X9VnDhR9/CBFHjFB2gSGKJ7Pr9N0gzmHUkhoUo=
			</data>
		</dict>
		<key>Modules/flutter_secure_storage.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/flutter_secure_storage.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			kxLZfpPemO5Ypt9ujF8nnbTQp8Cktb95exHe887212g=
			</data>
		</dict>
		<key>Modules/flutter_secure_storage.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			86YKozpplP+G13yYgse4c1vDR7ixqtjmLw7xLvEmaHA=
			</data>
		</dict>
		<key>Modules/flutter_secure_storage.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/flutter_secure_storage.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			wPEjBO6b1W6Ik+u1/9X2hZFx2cnWVYMl2iEa/G9tZp4=
			</data>
		</dict>
		<key>Modules/flutter_secure_storage.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			e3qb8wJQmJNypKALU7zHoy+KuGePHUJD/gZiFJORWXs=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			1U2/SV73WPOP2odM+avfw/5Gwkpsj/devNC7UP9KBTM=
			</data>
		</dict>
		<key>flutter_secure_storage.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			yY3BNgbjpk14Ldm5lEaYZN5dYR+gC3avoO050HNTBjo=
			</data>
		</dict>
		<key>flutter_secure_storage.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
