 swift-stdlib-tool /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/plummobile.app/Contents/Frameworks/device_info_plus.framework/Versions/A/device_info_plus /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/plummobile.app/Contents/Frameworks/device_info_plus.framework/device_info_plus /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/plummobile.app/Contents/Frameworks/flutter_secure_storage_macos.framework/Versions/A/flutter_secure_storage_macos /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/plummobile.app/Contents/Frameworks/flutter_secure_storage_macos.framework/flutter_secure_storage_macos /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/plummobile.app/Contents/Frameworks/path_provider_foundation.framework/Versions/A/path_provider_foundation /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/plummobile.app/Contents/Frameworks/path_provider_foundation.framework/path_provider_foundation /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/plummobile.app/Contents/Frameworks/shared_preferences_foundation.framework/Versions/A/shared_preferences_foundation /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/plummobile.app/Contents/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/macos/Build/Products/Debug/plummobile.app/Contents/MacOS/plummobile.debug.dylib 